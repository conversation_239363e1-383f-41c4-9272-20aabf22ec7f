import { system } from "@minecraft/server";
export const entitiesWithMusic = new Map([
    ["ditsh:ao_oni", "mob.ditsh.ao_oni.chase"],
    ["ditsh:armless", "mob.ditsh.armless.chase"],
    ["ditsh:headless_horseman", "mob.ditsh.headless_horseman.chase"],
    ["ditsh:herobrine", "mob.ditsh.herobrine.chase"],
    ["ditsh:jack_black", "mob.ditsh.jack_black.chase"],
    ["ditsh:jeff", "mob.ditsh.jeff.chase"],
    ["ditsh:mac_tonight", "mob.ditsh.mac_tonight.chase"],
    ["ditsh:mama_tattletail", "mob.ditsh.mama_tattletail.chase"],
    ["ditsh:murder_monkey", "mob.ditsh.murder_monkey.chase"],
    ["ditsh:mx", "mob.ditsh.mx.chase"],
    ["ditsh:nun", "mob.ditsh.nun.chase"],
    ["ditsh:rosemary", "mob.ditsh.rosemary.chase"],
    ["ditsh:scp096", "mob.ditsh.scp096.chase"],
    ["ditsh:scp173", "mob.ditsh.scp173.chase"],
    ["ditsh:scp049", "mob.ditsh.scp049.chase"],
    ["ditsh:grunt", "mob.ditsh.grunt.chase"],
    ["ditsh:sonic", "mob.ditsh.sonic.chase"],
    ["ditsh:specimen2", "mob.ditsh.specimen2.chase"]
]);
export const musicDurations = new Map([
    ["ditsh:ao_oni", 22.83 * 20],
    ["ditsh:armless", 22 * 20],
    ["ditsh:headless_horseman", 81 * 20],
    ["ditsh:herobrine", 24 * 20],
    ["ditsh:jack_black", 32 * 20],
    ["ditsh:jeff", 46 * 20],
    ["ditsh:mac_tonight", 30 * 20],
    ["ditsh:mama_tattletail", 39 * 20],
    ["ditsh:murder_monkey", 106 * 20],
    ["ditsh:mx", 10 * 20],
    ["ditsh:nun", 22 * 20],
    ["ditsh:rosemary", 89 * 20],
    ["ditsh:scp096", 14 * 20],
    ["ditsh:scp173", 5 * 20],
    ["ditsh:scp049", 11 * 20],
    ["ditsh:grunt", 10 * 20],
    ["ditsh:sonic", 14 * 20],
    ["ditsh:specimen2", 68 * 20]
]);
const globalMusicTracker = new Map();
function isMusicExpired(entityTypeId) {
    const tracker = globalMusicTracker.get(entityTypeId);
    if (!tracker)
        return true;
    const durationTicks = musicDurations.get(entityTypeId);
    if (!durationTicks)
        return true;
    const currentTick = system.currentTick;
    const elapsedTicks = currentTick - tracker.startTick;
    return elapsedTicks >= durationTicks;
}
export function cleanupExpiredMusic() {
    for (const [entityTypeId] of globalMusicTracker) {
        if (isMusicExpired(entityTypeId)) {
            globalMusicTracker.delete(entityTypeId);
        }
    }
}
function canEntityPlayMusic(entity) {
    const tracker = globalMusicTracker.get(entity.typeId);
    if (!tracker)
        return true;
    if (isMusicExpired(entity.typeId)) {
        globalMusicTracker.delete(entity.typeId);
        return true;
    }
    if (tracker.entityId === entity.id)
        return true;
    return false;
}
export function playMusicForEntity(entity, music) {
    cleanupExpiredMusic();
    if (!canEntityPlayMusic(entity)) {
        return;
    }
    const nearbyPlayers = getNearbyPlayers(entity, 256);
    let musicStarted = false;
    for (const player of nearbyPlayers) {
        let isCurrentlyPlaying = isPlayerPlayingMusic(player, entity.typeId);
        if (isCurrentlyPlaying && isMusicExpired(entity.typeId)) {
            player.setDynamicProperty(`${entity.typeId}_music`, false);
            isCurrentlyPlaying = false;
        }
        if (!isCurrentlyPlaying) {
            player.runCommand(`playsound ${music} @s ~ ~ ~ 1.0 1.0`);
            player.setDynamicProperty(`${entity.typeId}_music`, true);
            musicStarted = true;
        }
    }
    if (musicStarted) {
        globalMusicTracker.set(entity.typeId, {
            entityId: entity.id,
            startTick: system.currentTick
        });
    }
    return;
}
export async function stopMusicForEntity(entity, music) {
    const nearbyPlayers = getNearbyPlayers(entity, 256);
    for (const player of nearbyPlayers) {
        const isCurrentlyPlaying = isPlayerPlayingMusic(player, entity.typeId);
        if (isCurrentlyPlaying) {
            player.runCommand(`stopsound @s ${music}`);
            player.setDynamicProperty(`${entity.typeId}_music`, false);
        }
    }
    const tracker = globalMusicTracker.get(entity.typeId);
    if (tracker && tracker.entityId === entity.id) {
        globalMusicTracker.delete(entity.typeId);
    }
    return;
}
export async function continueMusicForEntity(entity, music) {
    const playMusic = entity.getProperty("ditsh:playing_music");
    if (playMusic) {
        await system.waitTicks(140);
        cleanupExpiredMusic();
        if (!canEntityPlayMusic(entity)) {
            return;
        }
        const nearbyPlayers = getNearbyPlayers(entity, 256);
        let musicStarted = false;
        for (const player of nearbyPlayers) {
            let isCurrentlyPlaying = isPlayerPlayingMusic(player, entity.typeId);
            if (isCurrentlyPlaying && isMusicExpired(entity.typeId)) {
                player.setDynamicProperty(`${entity.typeId}_music`, false);
                isCurrentlyPlaying = false;
            }
            if (!isCurrentlyPlaying) {
                player.runCommand(`playsound ${music} @s ~ ~ ~ 1.0 1.0`);
                player.setDynamicProperty(`${entity.typeId}_music`, true);
                musicStarted = true;
            }
        }
        if (musicStarted) {
            globalMusicTracker.set(entity.typeId, {
                entityId: entity.id,
                startTick: system.currentTick
            });
        }
    }
    return;
}
export function resetPlayerMusic(player) {
    for (const [entityTypeId, music] of entitiesWithMusic) {
        const isCurrentlyPlaying = isPlayerPlayingMusic(player, entityTypeId);
        if (isCurrentlyPlaying) {
            player.runCommand(`stopsound @s ${music}`);
            player.setDynamicProperty(`${entityTypeId}_music`, false);
        }
    }
    return;
}
function getNearbyPlayers(entity, range) {
    const players = entity.dimension.getPlayers({ location: entity.location, maxDistance: range });
    return players;
}
function isPlayerPlayingMusic(player, entityTypeId) {
    const playingMusic = player.getDynamicProperty(`${entityTypeId}_music`);
    return playingMusic ?? false;
}
